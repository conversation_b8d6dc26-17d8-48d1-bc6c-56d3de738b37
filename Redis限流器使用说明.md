# Redis限流器模块使用说明

## 概述

本模块提供了基于Redis的分布式限流功能，支持令牌桶和滑动窗口两种限流算法，通过配置文件可以灵活配置限流参数。

## 功能特性

- **分布式限流**：基于Redis实现，支持多实例部署
- **多种算法**：支持令牌桶（TOKEN_BUCKET）和滑动窗口（SLIDING_WINDOW）算法
- **配置化**：通过配置文件灵活配置限流参数
- **高可用**：异常情况下自动降级，不影响业务正常运行
- **多场景支持**：支持不同服务的独立限流配置

## 核心组件

### 1. RateLimiter接口
```java
public interface RateLimiter {
    boolean tryAcquire(String key);
    boolean tryAcquire(String key, int permits);
    boolean tryAcquire(String key, long timeoutMs);
    double acquire(String key);
    long getAvailablePermits(String key);
    void reset(String key);
    void setRate(String key, double permitsPerSecond);
}
```

### 2. RedisRateLimiter实现类
基于Redis的限流器实现，支持：
- 令牌桶算法：适用于允许突发流量的场景
- 滑动窗口算法：适用于需要严格控制流量的场景

### 3. RateLimiterQConfig配置类
通过QConfig读取配置文件，支持动态配置更新。

## 配置说明

### 配置文件：rate_limiter.properties

```properties
# 默认限流大小（每秒允许的请求数）
defaultRateLimit=100

# 默认时间窗口（秒）
defaultTimeWindow=1

# Redis key前缀
redisKeyPrefix=dsp_rate_limiter:

# 令牌桶容量（默认为限流大小的2倍）
bucketCapacity=200

# 令牌补充速率（每秒补充的令牌数）
refillRate=100

# 地理服务限流大小
geoServiceRateLimit=50

# 订单服务限流大小
orderServiceRateLimit=200

# 司机服务限流大小
driverServiceRateLimit=150

# 限流器过期时间（秒）
expireTime=3600

# 是否启用限流
enableRateLimit=true

# 限流算法类型：TOKEN_BUCKET（令牌桶）或 SLIDING_WINDOW（滑动窗口）
algorithmType=TOKEN_BUCKET

# 滑动窗口分片数量
slidingWindowSlices=10
```

## 使用方式

### 1. 基本使用

```java
@Component
public class SomeService {
    
    @Autowired
    @Qualifier("redisRateLimiter")
    private RateLimiter rateLimiter;
    
    public void doSomething() {
        String key = "service:operation:" + userId;
        
        // 尝试获取令牌
        if (rateLimiter.tryAcquire(key)) {
            // 执行业务逻辑
            performOperation();
        } else {
            // 限流触发，返回错误或等待
            throw new RateLimitExceededException("请求过于频繁，请稍后重试");
        }
    }
}
```

### 2. 带超时的获取

```java
// 尝试获取令牌，最多等待1秒
if (rateLimiter.tryAcquire(key, 1000L)) {
    performOperation();
} else {
    handleRateLimit();
}
```

### 3. 阻塞获取

```java
// 阻塞直到获取到令牌
double waitTime = rateLimiter.acquire(key);
logger.info("等待时间: {} 秒", waitTime);
performOperation();
```

### 4. 批量获取令牌

```java
// 一次获取5个令牌
if (rateLimiter.tryAcquire(key, 5)) {
    performBatchOperation();
}
```

### 5. 专用限流器使用

```java
@Component
public class GeoService {
    
    @Autowired
    @Qualifier("geoServiceRateLimiter")
    private RateLimiter geoRateLimiter;
    
    public void queryRoutes(Integer cityId) {
        String key = "geo_service:" + cityId;
        if (geoRateLimiter.tryAcquire(key)) {
            // 执行地理服务调用
        }
    }
}
```

## 限流算法说明

### 令牌桶算法（TOKEN_BUCKET）
- **特点**：允许突发流量，平滑限流
- **适用场景**：大部分业务场景，允许短时间内的流量突发
- **参数**：
  - `bucketCapacity`：桶容量
  - `refillRate`：令牌补充速率

### 滑动窗口算法（SLIDING_WINDOW）
- **特点**：严格控制时间窗口内的请求数量
- **适用场景**：需要严格控制QPS的场景
- **参数**：
  - `defaultTimeWindow`：时间窗口大小
  - `slidingWindowSlices`：窗口分片数量

## 监控和运维

### 1. 查看可用令牌数
```java
long availablePermits = rateLimiter.getAvailablePermits(key);
logger.info("可用令牌数: {}", availablePermits);
```

### 2. 重置限流器
```java
rateLimiter.reset(key);
logger.info("限流器已重置: {}", key);
```

### 3. 动态调整限流速率
```java
rateLimiter.setRate(key, 200.0); // 调整为每秒200个请求
```

## 异常处理

限流器在遇到Redis异常时会自动降级，返回true允许请求通过，确保业务不受影响。建议在业务代码中添加相应的监控和告警。

```java
try {
    if (rateLimiter.tryAcquire(key)) {
        performOperation();
    } else {
        handleRateLimit();
    }
} catch (Exception e) {
    logger.error("限流器异常", e);
    // 异常时允许请求通过
    performOperation();
}
```

## 性能考虑

1. **Redis连接池**：确保Redis连接池配置合理
2. **Key设计**：合理设计限流Key，避免热点Key
3. **过期时间**：设置合理的过期时间，避免内存泄漏
4. **监控告警**：监控限流器的使用情况和Redis性能

## 注意事项

1. 确保Redis服务的高可用性
2. 合理设置限流参数，避免过度限流影响业务
3. 在高并发场景下，注意Redis的性能瓶颈
4. 定期清理过期的限流数据
5. 建议在测试环境充分验证限流效果后再上线
