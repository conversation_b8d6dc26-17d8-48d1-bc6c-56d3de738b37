package com.ctrip.dcs.dsp.delay.limit;

/**
 * 限流器接口
 * 提供统一的限流方法签名
 * 
 * <AUTHOR>
 */
public interface RateLimiter {

    /**
     * 尝试获取令牌
     * 
     * @param key 限流key
     * @return 是否获取成功
     */
    boolean tryAcquire(String key);

    /**
     * 尝试获取指定数量的令牌
     * 
     * @param key 限流key
     * @param permits 需要获取的令牌数量
     * @return 是否获取成功
     */
    boolean tryAcquire(String key, int permits);

    /**
     * 尝试获取令牌，带超时
     * 
     * @param key 限流key
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否获取成功
     */
    boolean tryAcquire(String key, long timeoutMs);

    /**
     * 尝试获取指定数量的令牌，带超时
     * 
     * @param key 限流key
     * @param permits 需要获取的令牌数量
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否获取成功
     */
    boolean tryAcquire(String key, int permits, long timeoutMs);

    /**
     * 获取令牌（阻塞直到获取成功）
     * 
     * @param key 限流key
     * @return 等待时间（秒）
     */
    double acquire(String key);

    /**
     * 获取指定数量的令牌（阻塞直到获取成功）
     * 
     * @param key 限流key
     * @param permits 需要获取的令牌数量
     * @return 等待时间（秒）
     */
    double acquire(String key, int permits);

    /**
     * 获取当前可用令牌数量
     * 
     * @param key 限流key
     * @return 可用令牌数量
     */
    long getAvailablePermits(String key);

    /**
     * 重置限流器
     * 
     * @param key 限流key
     */
    void reset(String key);

    /**
     * 设置限流速率
     * 
     * @param key 限流key
     * @param permitsPerSecond 每秒允许的请求数
     */
    void setRate(String key, double permitsPerSecond);
}
