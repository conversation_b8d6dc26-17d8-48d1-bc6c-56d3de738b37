package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/**
 * Redis限流器配置类
 * 用于从配置文件读取限流相关参数
 * 
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("rate_limiter.properties")
public class RateLimiterQConfig {

    /**
     * 默认限流大小（每秒允许的请求数）
     */
    private Integer defaultRateLimit = 100;

    /**
     * 默认时间窗口（秒）
     */
    private Integer defaultTimeWindow = 1;

    /**
     * Redis key前缀
     */
    private String redisKeyPrefix = "rate_limiter:";

    /**
     * 令牌桶容量（默认为限流大小的2倍）
     */
    private Integer bucketCapacity = 200;

    /**
     * 令牌补充速率（每秒补充的令牌数）
     */
    private Integer refillRate = 100;

    /**
     * 地理服务限流大小
     */
    private Integer geoServiceRateLimit = 50;

    /**
     * 订单服务限流大小
     */
    private Integer orderServiceRateLimit = 200;

    /**
     * 司机服务限流大小
     */
    private Integer driverServiceRateLimit = 150;

    /**
     * 限流器过期时间（秒）
     */
    private Integer expireTime = 3600;

    /**
     * 是否启用限流
     */
    private Boolean enableRateLimit = true;

    /**
     * 限流算法类型：TOKEN_BUCKET（令牌桶）或 SLIDING_WINDOW（滑动窗口）
     */
    private String algorithmType = "TOKEN_BUCKET";

    /**
     * 滑动窗口分片数量
     */
    private Integer slidingWindowSlices = 10;
}
