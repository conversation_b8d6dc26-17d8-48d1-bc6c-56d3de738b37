package com.ctrip.dcs.dsp.delay.infrastructure.redis

import com.ctrip.dcs.dsp.delay.qconfig.RateLimiterQConfig
import credis.java.client.CacheProvider
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Redis限流器单元测试
 * 
 * <AUTHOR>
 */
class RedisRateLimiterTest extends Specification {

    def cacheProvider = Mock(CacheProvider)
    def rateLimiterConfig = Mock(RateLimiterQConfig)
    def redisRateLimiter = new RedisRateLimiter()

    def setup() {
        redisRateLimiter.cacheProvider = cacheProvider
        redisRateLimiter.rateLimiterConfig = rateLimiterConfig
        
        // 设置默认配置
        rateLimiterConfig.getEnableRateLimit() >> true
        rateLimiterConfig.getRedisKeyPrefix() >> "test_rate_limiter:"
        rateLimiterConfig.getAlgorithmType() >> "TOKEN_BUCKET"
        rateLimiterConfig.getBucketCapacity() >> 100
        rateLimiterConfig.getRefillRate() >> 10
        rateLimiterConfig.getDefaultRateLimit() >> 10
        rateLimiterConfig.getDefaultTimeWindow() >> 1
        rateLimiterConfig.getExpireTime() >> 3600
    }

    def "test tryAcquire with single permit - success"() {
        given: "Mock Redis返回足够的令牌"
        cacheProvider.get("test_rate_limiter:test_key:tokens") >> "10"
        cacheProvider.get("test_rate_limiter:test_key:last_refill") >> String.valueOf(System.currentTimeMillis())
        cacheProvider.setex(_, _, _) >> true

        when: "尝试获取1个令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key")

        then: "应该成功获取"
        result == true
    }

    def "test tryAcquire with single permit - failure"() {
        given: "Mock Redis返回令牌不足"
        cacheProvider.get("test_rate_limiter:test_key:tokens") >> "0"
        cacheProvider.get("test_rate_limiter:test_key:last_refill") >> String.valueOf(System.currentTimeMillis())
        cacheProvider.setex(_, _, _) >> true

        when: "尝试获取1个令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key")

        then: "应该获取失败"
        result == false
    }

    @Unroll
    def "test tryAcquire with multiple permits - #scenario"() {
        given: "Mock Redis返回指定数量的令牌"
        cacheProvider.get("test_rate_limiter:test_key:tokens") >> availableTokens
        cacheProvider.get("test_rate_limiter:test_key:last_refill") >> String.valueOf(System.currentTimeMillis())
        cacheProvider.setex(_, _, _) >> true

        when: "尝试获取指定数量的令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key", requestedPermits)

        then: "验证结果"
        result == expectedResult

        where:
        scenario        | availableTokens | requestedPermits | expectedResult
        "sufficient"    | "10"           | 5                | true
        "insufficient"  | "3"            | 5                | false
        "exact match"   | "5"            | 5                | true
    }

    def "test tryAcquire with timeout - success within timeout"() {
        given: "第一次失败，第二次成功"
        cacheProvider.get("test_rate_limiter:test_key:tokens") >>> ["0", "10"]
        cacheProvider.get("test_rate_limiter:test_key:last_refill") >> String.valueOf(System.currentTimeMillis())
        cacheProvider.setex(_, _, _) >> true

        when: "尝试获取令牌，带超时"
        boolean result = redisRateLimiter.tryAcquire("test_key", 100L)

        then: "应该在超时内成功"
        result == true
    }

    def "test acquire - blocking until success"() {
        given: "第一次失败，第二次成功"
        cacheProvider.get("test_rate_limiter:test_key:tokens") >>> ["0", "10"]
        cacheProvider.get("test_rate_limiter:test_key:last_refill") >> String.valueOf(System.currentTimeMillis())
        cacheProvider.setex(_, _, _) >> true

        when: "阻塞获取令牌"
        double waitTime = redisRateLimiter.acquire("test_key")

        then: "应该返回等待时间"
        waitTime >= 0
    }

    def "test getAvailablePermits"() {
        given: "Mock Redis返回可用令牌数"
        cacheProvider.hget("test_rate_limiter:test_key", "tokens") >> "15"

        when: "获取可用令牌数"
        long availablePermits = redisRateLimiter.getAvailablePermits("test_key")

        then: "应该返回正确的数量"
        availablePermits == 15
    }

    def "test getAvailablePermits - no data"() {
        given: "Mock Redis返回空值"
        cacheProvider.hget("test_rate_limiter:test_key", "tokens") >> null

        when: "获取可用令牌数"
        long availablePermits = redisRateLimiter.getAvailablePermits("test_key")

        then: "应该返回默认容量"
        availablePermits == 100
    }

    def "test reset"() {
        when: "重置限流器"
        redisRateLimiter.reset("test_key")

        then: "应该删除Redis中的数据"
        1 * cacheProvider.del("test_rate_limiter:test_key")
    }

    def "test rate limiter disabled"() {
        given: "限流器被禁用"
        rateLimiterConfig.getEnableRateLimit() >> false

        when: "尝试获取令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key")

        then: "应该直接返回成功"
        result == true
        0 * cacheProvider._
    }

    def "test sliding window algorithm"() {
        given: "配置为滑动窗口算法"
        rateLimiterConfig.getAlgorithmType() >> "SLIDING_WINDOW"
        cacheProvider.get("test_rate_limiter:test_key:count") >> "5"
        cacheProvider.get("test_rate_limiter:test_key:window") >> String.valueOf(System.currentTimeMillis())
        cacheProvider.setex(_, _, _) >> true

        when: "尝试获取令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key", 3)

        then: "应该使用滑动窗口算法"
        result == true
    }

    def "test exception handling"() {
        given: "Redis操作抛出异常"
        cacheProvider.get(_) >> { throw new RuntimeException("Redis error") }

        when: "尝试获取令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key")

        then: "应该返回true（异常时允许通过）"
        result == true
    }

    def "test token bucket refill calculation"() {
        given: "设置时间相关的Mock"
        long currentTime = System.currentTimeMillis()
        long lastRefillTime = currentTime - 1000 // 1秒前
        
        cacheProvider.get("test_rate_limiter:test_key:tokens") >> "5"
        cacheProvider.get("test_rate_limiter:test_key:last_refill") >> String.valueOf(lastRefillTime)
        cacheProvider.setex(_, _, _) >> true

        when: "尝试获取令牌"
        boolean result = redisRateLimiter.tryAcquire("test_key", 10)

        then: "应该考虑令牌补充"
        result == true // 5 + 10*1 = 15 tokens, 足够获取10个
    }
}
