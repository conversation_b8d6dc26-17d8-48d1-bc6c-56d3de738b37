package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import com.ctrip.dcs.dsp.delay.limit.RateLimiter;
import com.ctrip.dcs.dsp.delay.qconfig.RateLimiterQConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import credis.java.client.CacheProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redis限流器实现类
 * 使用Redis实现令牌桶限流算法
 * 
 * <AUTHOR>
 */
@Component
public class RedisRateLimiter implements RateLimiter {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiter.class);

    @Qualifier("redisCacheProvider")
    @Autowired
    private CacheProvider cacheProvider;

    @Autowired
    private RateLimiterQConfig rateLimiterConfig;

    /**
     * Lua脚本：令牌桶算法实现
     * 参数：key, capacity, refill_rate, requested_tokens, current_time
     * 返回：0表示获取失败，1表示获取成功
     */
    private static final String TOKEN_BUCKET_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local capacity = tonumber(ARGV[1])\n" +
        "local refill_rate = tonumber(ARGV[2])\n" +
        "local requested_tokens = tonumber(ARGV[3])\n" +
        "local current_time = tonumber(ARGV[4])\n" +
        "local expire_time = tonumber(ARGV[5])\n" +
        "\n" +
        "local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')\n" +
        "local tokens = tonumber(bucket[1]) or capacity\n" +
        "local last_refill = tonumber(bucket[2]) or current_time\n" +
        "\n" +
        "-- 计算需要补充的令牌数\n" +
        "local time_passed = current_time - last_refill\n" +
        "local new_tokens = math.min(capacity, tokens + time_passed * refill_rate / 1000)\n" +
        "\n" +
        "-- 检查是否有足够的令牌\n" +
        "if new_tokens >= requested_tokens then\n" +
        "    new_tokens = new_tokens - requested_tokens\n" +
        "    redis.call('HMSET', key, 'tokens', new_tokens, 'last_refill', current_time)\n" +
        "    redis.call('EXPIRE', key, expire_time)\n" +
        "    return 1\n" +
        "else\n" +
        "    redis.call('HMSET', key, 'tokens', new_tokens, 'last_refill', current_time)\n" +
        "    redis.call('EXPIRE', key, expire_time)\n" +
        "    return 0\n" +
        "end";

    /**
     * Lua脚本：滑动窗口算法实现
     */
    private static final String SLIDING_WINDOW_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local window_size = tonumber(ARGV[1])\n" +
        "local limit = tonumber(ARGV[2])\n" +
        "local current_time = tonumber(ARGV[3])\n" +
        "local expire_time = tonumber(ARGV[4])\n" +
        "\n" +
        "-- 清理过期的记录\n" +
        "redis.call('ZREMRANGEBYSCORE', key, 0, current_time - window_size * 1000)\n" +
        "\n" +
        "-- 获取当前窗口内的请求数\n" +
        "local current_requests = redis.call('ZCARD', key)\n" +
        "\n" +
        "if current_requests < limit then\n" +
        "    redis.call('ZADD', key, current_time, current_time)\n" +
        "    redis.call('EXPIRE', key, expire_time)\n" +
        "    return 1\n" +
        "else\n" +
        "    return 0\n" +
        "end";

    @Override
    public boolean tryAcquire(String key) {
        return tryAcquire(key, 1);
    }

    @Override
    public boolean tryAcquire(String key, int permits) {
        if (!rateLimiterConfig.getEnableRateLimit()) {
            return true;
        }

        try {
            String fullKey = rateLimiterConfig.getRedisKeyPrefix() + key;
            long currentTime = System.currentTimeMillis();

            if ("SLIDING_WINDOW".equals(rateLimiterConfig.getAlgorithmType())) {
                return executeSlidingWindowScript(fullKey, permits, currentTime);
            } else {
                return executeTokenBucketScript(fullKey, permits, currentTime);
            }
        } catch (Exception e) {
            logger.error("RedisRateLimiter.tryAcquire", "Rate limit check failed for key: {}, error: {}", key, e);
            // 发生异常时，默认允许通过
            return true;
        }
    }

    @Override
    public boolean tryAcquire(String key, long timeoutMs) {
        return tryAcquire(key, 1, timeoutMs);
    }

    @Override
    public boolean tryAcquire(String key, int permits, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (tryAcquire(key, permits)) {
                return true;
            }
            try {
                Thread.sleep(10); // 短暂休眠后重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }

    @Override
    public double acquire(String key) {
        return acquire(key, 1);
    }

    @Override
    public double acquire(String key, int permits) {
        long startTime = System.currentTimeMillis();
        while (!tryAcquire(key, permits)) {
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return (System.currentTimeMillis() - startTime) / 1000.0;
    }

    @Override
    public long getAvailablePermits(String key) {
        try {
            String fullKey = rateLimiterConfig.getRedisKeyPrefix() + key;
            String tokens = cacheProvider.hget(fullKey, "tokens");
            return tokens != null ? Long.parseLong(tokens) : rateLimiterConfig.getBucketCapacity();
        } catch (Exception e) {
            logger.error("RedisRateLimiter.getAvailablePermits", "Get available permits failed for key: {}, error: {}", key, e);
            return 0;
        }
    }

    @Override
    public void reset(String key) {
        try {
            String fullKey = rateLimiterConfig.getRedisKeyPrefix() + key;
            cacheProvider.del(fullKey);
        } catch (Exception e) {
            logger.error("RedisRateLimiter.reset", "Reset rate limiter failed for key: {}, error: {}", key, e);
        }
    }

    @Override
    public void setRate(String key, double permitsPerSecond) {
        // 这个方法在Redis实现中不需要特别处理，因为速率是通过配置文件控制的
        logger.info("RedisRateLimiter.setRate", "Set rate for key: {}, rate: {}", key, permitsPerSecond);
    }

    /**
     * 执行令牌桶算法脚本
     */
    private boolean executeTokenBucketScript(String key, int permits, long currentTime) {
        try {
            // 注意：这里使用简化的实现，实际项目中可能需要使用Redis的EVAL命令
            // 由于当前使用的CacheProvider可能不支持Lua脚本，这里使用简化的实现
            return executeSimpleTokenBucket(key, permits, currentTime);
        } catch (Exception e) {
            logger.error("RedisRateLimiter.executeTokenBucketScript", "Execute token bucket script failed: {}", e);
            return true; // 异常时允许通过
        }
    }

    /**
     * 执行滑动窗口算法脚本
     */
    private boolean executeSlidingWindowScript(String key, int permits, long currentTime) {
        try {
            // 简化的滑动窗口实现
            return executeSimpleSlidingWindow(key, permits, currentTime);
        } catch (Exception e) {
            logger.error("RedisRateLimiter.executeSlidingWindowScript", "Execute sliding window script failed: {}", e);
            return true; // 异常时允许通过
        }
    }

    /**
     * 简化的令牌桶实现（不使用Lua脚本）
     */
    private boolean executeSimpleTokenBucket(String key, int permits, long currentTime) {
        String tokensKey = key + ":tokens";
        String lastRefillKey = key + ":last_refill";
        
        // 获取当前令牌数和上次补充时间
        String tokensStr = cacheProvider.get(tokensKey);
        String lastRefillStr = cacheProvider.get(lastRefillKey);
        
        double tokens = tokensStr != null ? Double.parseDouble(tokensStr) : rateLimiterConfig.getBucketCapacity();
        long lastRefill = lastRefillStr != null ? Long.parseLong(lastRefillStr) : currentTime;
        
        // 计算需要补充的令牌数
        long timePassed = currentTime - lastRefill;
        double newTokens = Math.min(rateLimiterConfig.getBucketCapacity(), 
                                   tokens + timePassed * rateLimiterConfig.getRefillRate() / 1000.0);
        
        // 检查是否有足够的令牌
        if (newTokens >= permits) {
            newTokens -= permits;
            cacheProvider.setex(tokensKey, String.valueOf(newTokens), rateLimiterConfig.getExpireTime());
            cacheProvider.setex(lastRefillKey, String.valueOf(currentTime), rateLimiterConfig.getExpireTime());
            return true;
        } else {
            cacheProvider.setex(tokensKey, String.valueOf(newTokens), rateLimiterConfig.getExpireTime());
            cacheProvider.setex(lastRefillKey, String.valueOf(currentTime), rateLimiterConfig.getExpireTime());
            return false;
        }
    }

    /**
     * 简化的滑动窗口实现
     */
    private boolean executeSimpleSlidingWindow(String key, int permits, long currentTime) {
        String countKey = key + ":count";
        String windowKey = key + ":window";
        
        // 获取当前窗口的开始时间
        String windowStartStr = cacheProvider.get(windowKey);
        long windowStart = windowStartStr != null ? Long.parseLong(windowStartStr) : currentTime;
        
        // 检查是否需要重置窗口
        if (currentTime - windowStart >= rateLimiterConfig.getDefaultTimeWindow() * 1000) {
            windowStart = currentTime;
            cacheProvider.setex(windowKey, String.valueOf(windowStart), rateLimiterConfig.getExpireTime());
            cacheProvider.setex(countKey, "0", rateLimiterConfig.getExpireTime());
        }
        
        // 获取当前计数
        String countStr = cacheProvider.get(countKey);
        int count = countStr != null ? Integer.parseInt(countStr) : 0;
        
        // 检查是否超过限制
        if (count + permits <= rateLimiterConfig.getDefaultRateLimit()) {
            cacheProvider.setex(countKey, String.valueOf(count + permits), rateLimiterConfig.getExpireTime());
            return true;
        } else {
            return false;
        }
    }
}
