package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import com.ctrip.dcs.dsp.delay.limit.RateLimiter;
import com.ctrip.dcs.dsp.delay.qconfig.RateLimiterQConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redis限流器配置类
 * 用于配置Redis限流器相关的Bean
 * 
 * <AUTHOR>
 */
@Configuration
public class RedisRateLimiterConfig {

    @Autowired
    private RateLimiterQConfig rateLimiterQConfig;

    /**
     * 创建Redis限流器Bean
     */
    @Bean("redisRateLimiter")
    public RateLimiter redisRateLimiter() {
        return new RedisRateLimiter();
    }

    /**
     * 创建地理服务专用限流器
     */
    @Bean("geoServiceRateLimiter")
    public RateLimiter geoServiceRateLimiter() {
        RedisRateLimiter rateLimiter = new RedisRateLimiter();
        // 可以在这里设置特定的配置
        return rateLimiter;
    }

    /**
     * 创建订单服务专用限流器
     */
    @Bean("orderServiceRateLimiter")
    public RateLimiter orderServiceRateLimiter() {
        RedisRateLimiter rateLimiter = new RedisRateLimiter();
        return rateLimiter;
    }

    /**
     * 创建司机服务专用限流器
     */
    @Bean("driverServiceRateLimiter")
    public RateLimiter driverServiceRateLimiter() {
        RedisRateLimiter rateLimiter = new RedisRateLimiter();
        return rateLimiter;
    }
}
