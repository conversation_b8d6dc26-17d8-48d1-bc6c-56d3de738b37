# Redis???????
# ??????????

# ????????????????
defaultRateLimit=100

# ?????????
defaultTimeWindow=1

# Redis key??
redisKeyPrefix=dsp_rate_limiter:

# ??????????????2??
bucketCapacity=200

# ????????????????
refillRate=100

# ????????
geoServiceRateLimit=50

# ????????
orderServiceRateLimit=200

# ????????
driverServiceRateLimit=150

# ??????????
expireTime=3600

# ??????
enableRateLimit=true

# ???????TOKEN_BUCKET?????? SLIDING_WINDOW??????
algorithmType=TOKEN_BUCKET

# ????????
slidingWindowSlices=10
